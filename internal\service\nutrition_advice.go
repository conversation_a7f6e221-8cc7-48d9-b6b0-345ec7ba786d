package service

import (
	"context"
	"shikeyinxiang-goframe/internal/model"
)

// INutritionAdvice 营养建议管理服务接口
type INutritionAdvice interface {
	// GetAllAdvices 获取所有营养建议
	GetAllAdvices(ctx context.Context) ([]*model.NutritionAdviceInfo, error)

	// GetAdviceByID 根据ID获取营养建议
	GetAdviceByID(ctx context.Context, id int64) (*model.NutritionAdviceInfo, error)

	// CreateAdvice 创建营养建议
	CreateAdvice(ctx context.Context, in *model.NutritionAdviceCreateInput) (*model.NutritionAdviceInfo, error)

	// UpdateAdvice 更新营养建议
	UpdateAdvice(ctx context.Context, in *model.NutritionAdviceUpdateInput) (*model.NutritionAdviceInfo, error)

	// DeleteAdvice 删除营养建议
	DeleteAdvice(ctx context.Context, id int64) error

	// GetAdvicesByConditionType 根据条件类型获取营养建议
	GetAdvicesByConditionType(ctx context.Context, conditionType string) ([]*model.NutritionAdviceInfo, error)
}

var (
	localNutritionAdvice INutritionAdvice
)

// RegisterNutritionAdvice 注册营养建议管理服务
func RegisterNutritionAdvice(i INutritionAdvice) {
	localNutritionAdvice = i
}

// NutritionAdvice 获取营养建议管理服务
func NutritionAdvice() INutritionAdvice {
	if localNutritionAdvice == nil {
		panic("implement not found for interface INutritionAdvice, forgot register?")
	}
	return localNutritionAdvice
}
