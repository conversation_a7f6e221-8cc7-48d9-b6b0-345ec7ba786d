package nutrition

import (
	"context"

	"shikeyinxiang-goframe/api/nutrition/v1"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) AdminAdviceList(ctx context.Context, req *v1.AdminAdviceListReq) (res *v1.AdminAdviceListRes, err error) {
	// 1. 调用业务逻辑
	adviceList, err := service.NutritionAdvice().GetAllAdvices(ctx)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 2. 转换建议列表格式
	var advices []v1.NutritionAdviceItem
	for _, advice := range adviceList {
		advices = append(advices, v1.NutritionAdviceItem{
			Id:            advice.Id,
			Type:          advice.Type,
			Title:         advice.Title,
			Description:   advice.Description,
			ConditionType: advice.ConditionType,
			MinPercentage: advice.MinPercentage,
			MaxPercentage: advice.MaxPercentage,
			IsDefault:     advice.IsDefault,
			Priority:      advice.Priority,
			Status:        advice.Status,
			CreatedAt:     advice.CreatedAt.String(),
			UpdatedAt:     advice.UpdatedAt.String(),
		})
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.AdminAdviceListRes{
		List: advices,
	}

	return res, nil
}
