// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package dashboard

import (
	"context"

	"shikeyinxiang-goframe/api/dashboard/v1"
)

type IDashboardV1 interface {
	NutritionTrend(ctx context.Context, req *v1.NutritionTrendReq) (res *v1.NutritionTrendRes, err error)
	LatestDietRecords(ctx context.Context, req *v1.LatestDietRecordsReq) (res *v1.LatestDietRecordsRes, err error)
	DietRecordDetail(ctx context.Context, req *v1.DietRecordDetailReq) (res *v1.DietRecordDetailRes, err error)
	PopularFoods(ctx context.Context, req *v1.PopularFoodsReq) (res *v1.PopularFoodsRes, err error)
}
