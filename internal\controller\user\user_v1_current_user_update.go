package user

import (
	"context"

	"shikeyinxiang-goframe/api/user/v1"
	"shikeyinxiang-goframe/internal/consts"
	"shikeyinxiang-goframe/internal/middleware"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) CurrentUserUpdate(ctx context.Context, req *v1.CurrentUserUpdateReq) (res *v1.CurrentUserUpdateRes, err error) {
	// 1. 从上下文获取当前用户信息
	user := ctx.Value(consts.CtxUserInfo).(*middleware.UserInfo)

	// 2. 参数转换：API请求参数 → 业务层Input
	input := &model.UserUpdateInput{
		UserId:    user.UserId,
		Username:  req.Username,
		Email:     req.Email,
		Phone:     req.Phone,
		AvatarUrl: req.AvatarUrl,
	}

	// 3. 调用业务逻辑
	output, err := service.User().UpdateUser(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 4. 参数转换：业务层Output → API响应
	res = &v1.CurrentUserUpdateRes{
		Success: output.Success,
		Message: output.Message,
	}

	return res, nil
}
