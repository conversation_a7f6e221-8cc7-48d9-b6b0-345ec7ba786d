package service

import (
	"context"
	"shikeyinxiang-goframe/internal/model"
)

// INutrition 营养分析服务接口
type INutrition interface {
	// GetDailyNutritionStat 获取用户日营养统计
	GetDailyNutritionStat(ctx context.Context, in *model.NutritionDailyStatInput) (*model.NutritionDailyStatOutput, error)

	// GetNutritionTrend 获取用户营养趋势
	GetNutritionTrend(ctx context.Context, in *model.NutritionTrendInput) (*model.NutritionTrendOutput, error)

	// GetNutritionDetails 获取用户营养摄入详情
	GetNutritionDetails(ctx context.Context, in *model.NutritionDetailsInput) (*model.NutritionDetailsOutput, error)

	// GetNutritionAdvice 获取用户营养建议
	GetNutritionAdvice(ctx context.Context, in *model.NutritionAdviceInput) (*model.NutritionAdviceOutput, error)

	// CalculateNutritionComplianceRate 计算指定日期的营养达标率
	CalculateNutritionComplianceRate(ctx context.Context, in *model.NutritionComplianceRateInput) (*model.NutritionComplianceRateOutput, error)

	// GetAllNutritionTrend 获取所有用户营养趋势(供仪表盘使用)
	GetAllNutritionTrend(ctx context.Context, in *model.NutritionAllTrendInput) (*model.NutritionAllTrendOutput, error)

	// GetAdminNutritionTrend 获取所有用户营养趋势数据(管理员)
	GetAdminNutritionTrend(ctx context.Context, period string) (map[string]interface{}, error)
}

var (
	localNutrition INutrition
)

// RegisterNutrition 注册营养分析服务
func RegisterNutrition(i INutrition) {
	localNutrition = i
}

// Nutrition 获取营养分析服务
func Nutrition() INutrition {
	if localNutrition == nil {
		panic("implement not found for interface INutrition, forgot register?")
	}
	return localNutrition
}
