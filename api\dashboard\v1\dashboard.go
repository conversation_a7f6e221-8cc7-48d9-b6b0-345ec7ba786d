package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// NutritionTrendReq 营养摄入趋势请求
type NutritionTrendReq struct {
	g.Meta `path:"/dashboard/nutrition-trend" method:"get" tags:"Dashboard" summary:"获取营养摄入趋势数据"`
	Period string `json:"period" v:"in:week,month,year" dc:"时间周期：week(周)、month(月)、year(年)" default:"month"`
}

// NutritionTrendRes 营养摄入趋势响应
type NutritionTrendRes struct {
	Period      string    `json:"period" dc:"时间周期"`
	DateList    []string  `json:"dateList" dc:"日期列表"`
	CalorieList []float64 `json:"calorieList" dc:"卡路里列表"`
	ProteinList []float64 `json:"proteinList" dc:"蛋白质列表"`
	CarbsList   []float64 `json:"carbsList" dc:"碳水化合物列表"`
	FatList     []float64 `json:"fatList" dc:"脂肪列表"`
	DataPoints  int       `json:"dataPoints" dc:"数据点数量"`
}

// LatestDietRecordsReq 最新饮食记录请求
type LatestDietRecordsReq struct {
	g.Meta    `path:"/dashboard/latest-diet-records" method:"get" tags:"Dashboard" summary:"获取最新饮食记录列表"`
	Page      int    `json:"page" v:"min:1" dc:"页码" default:"1"`
	Size      int    `json:"size" v:"min:1,max:100" dc:"每页数量" default:"10"`
	StartDate string `json:"startDate" dc:"开始日期 YYYY-MM-DD"`
	EndDate   string `json:"endDate" dc:"结束日期 YYYY-MM-DD"`
}

// LatestDietRecordsRes 最新饮食记录响应
type LatestDietRecordsRes struct {
	List  []DietRecordItem `json:"list" dc:"饮食记录列表"`
	Total int64            `json:"total" dc:"总数"`
	Page  int              `json:"page" dc:"当前页"`
	Size  int              `json:"size" dc:"每页数量"`
}

// DietRecordItem 饮食记录项
type DietRecordItem struct {
	Id           int64       `json:"id" dc:"记录ID"`
	UserId       int64       `json:"userId" dc:"用户ID"`
	Username     string      `json:"username" dc:"用户名"`
	MealType     string      `json:"mealType" dc:"餐次类型"`
	RecordDate   *gtime.Time `json:"recordDate" dc:"记录日期"`
	TotalCalorie float64     `json:"totalCalorie" dc:"总卡路里"`
	TotalProtein float64     `json:"totalProtein" dc:"总蛋白质"`
	TotalCarbs   float64     `json:"totalCarbs" dc:"总碳水化合物"`
	TotalFat     float64     `json:"totalFat" dc:"总脂肪"`
	CreatedAt    *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// DietRecordDetailReq 饮食记录详情请求
type DietRecordDetailReq struct {
	g.Meta   `path:"/dashboard/diet-record/{recordId}" method:"get" tags:"Dashboard" summary:"获取饮食记录详情"`
	RecordId int64 `json:"recordId" v:"required|min:1" dc:"记录ID"`
}

// DietRecordDetailRes 饮食记录详情响应
type DietRecordDetailRes struct {
	Id           int64            `json:"id" dc:"记录ID"`
	UserId       int64            `json:"userId" dc:"用户ID"`
	Username     string           `json:"username" dc:"用户名"`
	MealType     string           `json:"mealType" dc:"餐次类型"`
	RecordDate   *gtime.Time      `json:"recordDate" dc:"记录日期"`
	TotalCalorie float64          `json:"totalCalorie" dc:"总卡路里"`
	TotalProtein float64          `json:"totalProtein" dc:"总蛋白质"`
	TotalCarbs   float64          `json:"totalCarbs" dc:"总碳水化合物"`
	TotalFat     float64          `json:"totalFat" dc:"总脂肪"`
	Foods        []DietRecordFood `json:"foods" dc:"食物列表"`
	CreatedAt    *gtime.Time      `json:"createdAt" dc:"创建时间"`
	UpdatedAt    *gtime.Time      `json:"updatedAt" dc:"更新时间"`
}

// DietRecordFood 饮食记录中的食物
type DietRecordFood struct {
	FoodId   int64   `json:"foodId" dc:"食物ID"`
	FoodName string  `json:"foodName" dc:"食物名称"`
	Quantity float64 `json:"quantity" dc:"数量"`
	Unit     string  `json:"unit" dc:"单位"`
	Calorie  float64 `json:"calorie" dc:"卡路里"`
	Protein  float64 `json:"protein" dc:"蛋白质"`
	Carbs    float64 `json:"carbs" dc:"碳水化合物"`
	Fat      float64 `json:"fat" dc:"脂肪"`
}

// PopularFoodsReq 热门食物统计请求
type PopularFoodsReq struct {
	g.Meta `path:"/dashboard/popular-foods" method:"get" tags:"Dashboard" summary:"获取热门食物统计"`
	Period string `json:"period" v:"in:week,month,quarter" dc:"时间周期：week(周)、month(月)、quarter(季度)" default:"month"`
	Limit  int    `json:"limit" v:"min:1,max:50" dc:"返回数量限制" default:"10"`
}

// PopularFoodsRes 热门食物统计响应
type PopularFoodsRes struct {
	Period string        `json:"period" dc:"时间周期"`
	Foods  []PopularFood `json:"foods" dc:"热门食物列表"`
}

// PopularFood 热门食物项
type PopularFood struct {
	FoodId       int64   `json:"foodId" dc:"食物ID"`
	FoodName     string  `json:"foodName" dc:"食物名称"`
	CategoryName string  `json:"categoryName" dc:"分类名称"`
	UsageCount   int64   `json:"usageCount" dc:"使用次数"`
	UserCount    int64   `json:"userCount" dc:"使用用户数"`
	AvgCalorie   float64 `json:"avgCalorie" dc:"平均卡路里"`
}
