package diet

import (
	"context"

	"shikeyinxiang-goframe/api/diet/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) DietRecordDetail(ctx context.Context, req *v1.DietRecordDetailReq) (res *v1.DietRecordDetailRes, err error) {
	// 1. 调用业务逻辑
	output, err := service.Diet().GetDietRecordDetail(ctx, req.Id)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 2. 转换食物列表格式
	var foods []v1.DietRecordFoodItem
	for _, food := range output.Record.Foods {
		foods = append(foods, v1.DietRecordFoodItem{
			FoodId:   food.FoodId,
			Name:     food.Name,
			Amount:   food.Amount,
			Unit:     food.Unit,
			Calories: food.Calories,
			Protein:  food.Protein,
			Fat:      food.Fat,
			Carbs:    food.Carbs,
			Grams:    food.Grams,
		})
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.DietRecordDetailRes{
		Id:           output.Record.Id,
		UserId:       output.Record.UserId,
		Date:         output.Record.Date,
		Time:         output.Record.Time,
		MealType:     output.Record.MealType,
		Remark:       output.Record.Remark,
		TotalCalorie: output.Record.TotalCalorie,
		Foods:        foods,
		CreateTime:   output.Record.CreateTime.String(),
		UpdateTime:   output.Record.UpdateTime.String(),
	}

	return res, nil
}
