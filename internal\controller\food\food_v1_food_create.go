package food

import (
	"context"

	"shikeyinxiang-goframe/api/food/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) FoodCreate(ctx context.Context, req *v1.FoodCreateReq) (res *v1.FoodCreateRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.FoodCreateInput{
		Name:       req.Name,
		Measure:    req.Measure,
		Grams:      req.Grams,
		Calories:   req.Calories,
		Protein:    req.Protein,
		Fat:        req.Fat,
		SatFat:     req.SatFat,
		Carbs:      req.Carbs,
		CategoryId: req.CategoryId,
		ImageUrl:   req.ImageUrl,
	}

	// 2. 调用业务逻辑
	foodInfo, err := service.Food().CreateFood(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.FoodCreateRes{
		Id:         foodInfo.Id,
		Name:       foodInfo.Name,
		Measure:    foodInfo.Measure,
		Grams:      foodInfo.Grams,
		Calories:   foodInfo.Calories,
		Protein:    foodInfo.Protein,
		Fat:        foodInfo.Fat,
		SatFat:     foodInfo.SatFat,
		Carbs:      foodInfo.Carbs,
		Category:   foodInfo.Category,
		CategoryId: foodInfo.CategoryId,
		ImageUrl:   foodInfo.ImageUrl,
		Unit:       req.Unit,
		Desc:       req.Desc,
		Remark:     req.Remark,
	}

	return res, nil
}
