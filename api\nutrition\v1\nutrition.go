package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// 每日营养统计请求
type DailyNutritionReq struct {
	g.Meta `path:"/daily" tags:"Nutrition" method:"get" summary:"获取每日营养统计"`
	Date   string `json:"date" v:"required|date" dc:"日期，格式：yyyy-MM-dd"`
}

type DailyNutritionRes struct {
	Date              string  `json:"date" dc:"日期"`
	Calorie           int     `json:"calorie" dc:"卡路里/热量（千卡）"`
	Protein           float64 `json:"protein" dc:"蛋白质（克）"`
	Carbs             float64 `json:"carbs" dc:"碳水化合物（克）"`
	Fat               float64 `json:"fat" dc:"脂肪（克）"`
	CaloriePercentage float64 `json:"caloriePercentage" dc:"热量目标达成百分比"`
	ProteinPercentage float64 `json:"proteinPercentage" dc:"蛋白质目标达成百分比"`
	CarbsPercentage   float64 `json:"carbsPercentage" dc:"碳水目标达成百分比"`
	FatPercentage     float64 `json:"fatPercentage" dc:"脂肪目标达成百分比"`
}

// 营养趋势分析请求
type NutritionTrendReq struct {
	g.Meta    `path:"/trend" tags:"Nutrition" method:"get" summary:"获取营养趋势分析"`
	StartDate string `json:"startDate" v:"required|date" dc:"开始日期"`
	EndDate   string `json:"endDate" v:"required|date" dc:"结束日期"`
	Period    string `json:"period" v:"in:week,month,year" dc:"时间周期" default:"week"`
}

type NutritionTrendRes struct {
	DateList    []string  `json:"dateList" dc:"日期列表"`
	CalorieList []int     `json:"calorieList" dc:"卡路里/热量列表（千卡）"`
	ProteinList []float64 `json:"proteinList" dc:"蛋白质列表（克）"`
	CarbsList   []float64 `json:"carbsList" dc:"碳水化合物列表（克）"`
	FatList     []float64 `json:"fatList" dc:"脂肪列表（克）"`
	Period      string    `json:"period" dc:"时间周期"`
	DataPoints  int       `json:"dataPoints" dc:"数据点数量"`
}

// 营养摄入详情请求
type NutritionDetailsReq struct {
	g.Meta `path:"/details" tags:"Nutrition" method:"get" summary:"获取营养摄入详情"`
	Date   string `json:"date" v:"required|date" dc:"日期，格式：yyyy-MM-dd"`
}

type NutritionDetailsRes struct {
	Details []NutritionDetailItem `json:"details" dc:"营养详情列表"`
}

type NutritionDetailItem struct {
	Name       string  `json:"name" dc:"营养成分名称"`
	Value      float64 `json:"value" dc:"摄入量"`
	Unit       string  `json:"unit" dc:"单位"`
	Target     float64 `json:"target" dc:"目标值"`
	Percentage float64 `json:"percentage" dc:"达成百分比"`
	Status     string  `json:"status" dc:"状态：normal/low/high"`
}

// 营养建议请求
type NutritionAdviceReq struct {
	g.Meta `path:"/advice" tags:"Nutrition" method:"get" summary:"获取营养建议"`
	Date   string `json:"date" v:"date" dc:"日期，格式：yyyy-MM-dd，默认当天"`
	UserId *int64 `json:"userId" dc:"用户ID（管理员查询用）"`
}

type NutritionAdviceRes struct {
	Advices []NutritionAdvice `json:"advices" dc:"营养建议列表"`
}

type NutritionAdvice struct {
	Type        string `json:"type" dc:"建议类型"`
	Title       string `json:"title" dc:"建议标题"`
	Description string `json:"description" dc:"建议描述"`
}

// 健康报告请求
type HealthReportReq struct {
	g.Meta `path:"/health/report" tags:"Nutrition" method:"get" summary:"获取健康报告"`
	Date   string `json:"date" v:"date" dc:"日期，格式：yyyy-MM-dd，默认当天"`
}

type HealthReportRes struct {
	Date             string            `json:"date" dc:"日期"`
	NutritionStat    DailyNutritionRes `json:"nutritionStat" dc:"营养统计"`
	Advices          []NutritionAdvice `json:"advices" dc:"营养建议列表"`
	WeeklyProgress   WeeklyProgress    `json:"weeklyProgress" dc:"周进度"`
	Suggestion       string            `json:"suggestion" dc:"建议"`
	HealthScore      int               `json:"healthScore" dc:"健康分数"`
	ScoreChange      int               `json:"scoreChange" dc:"分数变化"`
	NutritionBalance NutritionBalance  `json:"nutritionBalance" dc:"营养平衡"`
}

type WeeklyProgress struct {
	Week           int     `json:"week" dc:"周数"`
	AvgCalorie     float64 `json:"avgCalorie" dc:"平均热量"`
	AvgProtein     float64 `json:"avgProtein" dc:"平均蛋白质"`
	AvgCarbs       float64 `json:"avgCarbs" dc:"平均碳水"`
	AvgFat         float64 `json:"avgFat" dc:"平均脂肪"`
	ComplianceRate float64 `json:"complianceRate" dc:"达标率"`
}

type NutritionBalance struct {
	ProteinRatio float64 `json:"proteinRatio" dc:"蛋白质比例"`
	CarbsRatio   float64 `json:"carbsRatio" dc:"碳水比例"`
	FatRatio     float64 `json:"fatRatio" dc:"脂肪比例"`
	IsBalanced   bool    `json:"isBalanced" dc:"是否平衡"`
}

// 营养达标率请求（管理员功能）
type ComplianceRateReq struct {
	g.Meta `path:"/compliance-rate" tags:"Nutrition" method:"get" summary:"获取营养达标率"`
	Date   string `json:"date" v:"date" dc:"日期，格式：yyyy-MM-dd，默认当天"`
}

type ComplianceRateRes struct {
	Date           string  `json:"date" dc:"日期"`
	ComplianceRate float64 `json:"complianceRate" dc:"营养达标率（百分比，0-100）"`
	TotalUsers     int64   `json:"totalUsers" dc:"总用户数"`
	CompliantUsers int64   `json:"compliantUsers" dc:"达标用户数"`
}
