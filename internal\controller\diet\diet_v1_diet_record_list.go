package diet

import (
	"context"

	"shikeyinxiang-goframe/api/diet/v1"
	"shikeyinxiang-goframe/internal/consts"
	"shikeyinxiang-goframe/internal/middleware"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) DietRecordList(ctx context.Context, req *v1.DietRecordListReq) (res *v1.DietRecordListRes, err error) {
	// 1. 从上下文获取当前用户信息
	user := ctx.Value(consts.CtxUserInfo).(*middleware.UserInfo)

	// 2. 参数转换：API请求参数 → 业务层Input
	input := &model.DietRecordQueryInput{
		UserId:   user.UserId,
		Page:     req.Page,
		Size:     req.Size,
		Date:     req.Date,
		MealType: req.MealType,
	}

	// 3. 调用业务逻辑
	output, err := service.Diet().GetDietRecordList(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 4. 转换记录列表格式
	var records []v1.DietRecord
	for _, record := range output.Records {
		// 转换食物列表
		var foods []v1.DietRecordFoodItem
		for _, food := range record.Foods {
			foods = append(foods, v1.DietRecordFoodItem{
				FoodId:   food.FoodId,
				Name:     food.Name,
				Amount:   food.Amount,
				Unit:     food.Unit,
				Calories: food.Calories,
				Protein:  food.Protein,
				Fat:      food.Fat,
				Carbs:    food.Carbs,
				Grams:    food.Grams,
			})
		}

		records = append(records, v1.DietRecord{
			Id:           record.Id,
			UserId:       record.UserId,
			Date:         record.Date,
			Time:         record.Time,
			MealType:     record.MealType,
			Remark:       record.Remark,
			TotalCalorie: record.TotalCalorie,
			Foods:        foods,
			CreateTime:   record.CreateTime.String(),
			UpdateTime:   record.UpdateTime.String(),
		})
	}

	// 5. 参数转换：业务层Output → API响应
	res = &v1.DietRecordListRes{
		List:  records,
		Total: output.Total,
		Page:  output.Page,
		Size:  output.Size,
	}

	return res, nil
}
