package food

import (
	"context"

	"shikeyinxiang-goframe/api/food/v1"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) CategoryList(ctx context.Context, req *v1.CategoryListReq) (res *v1.CategoryListRes, err error) {
	// 1. 调用业务逻辑 - GetCategoryList方法接受current和size参数
	output, err := service.FoodCategory().GetCategoryList(ctx, req.Page, req.Size)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 2. 转换分类列表格式
	var categoryList []v1.FoodCategory
	for _, category := range output.List {
		categoryList = append(categoryList, v1.FoodCategory{
			Id:          int(category.Id),
			Name:        category.Name,
			Description: category.Description,
			Color:       category.Color,
			SortOrder:   category.SortOrder,
			FoodCount:   category.FoodCount,
		})
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.CategoryListRes{
		List:  categoryList,
		Total: output.Total,
		Page:  output.Page,
		Size:  output.Size,
	}

	return res, nil
}
