package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// 添加饮食记录请求
type DietRecordAddReq struct {
	g.Meta       `path:"/" tags:"Diet" method:"post" summary:"添加饮食记录"`
	Date         string               `json:"date" v:"required|date" dc:"记录日期"`
	Time         string               `json:"time" v:"required" dc:"记录时间"`
	MealType     string               `json:"mealType" v:"required|in:breakfast,lunch,dinner,snacks" dc:"餐次类型"`
	Remark       string               `json:"remark" dc:"备注信息"`
	TotalCalorie float64              `json:"totalCalorie" v:"min:0" dc:"总热量(千卡)"`
	Foods        []DietRecordFoodItem `json:"foods" v:"required" dc:"食物列表"`
}

type DietRecordAddRes struct {
	RecordId int64 `json:"recordId" dc:"记录ID"`
}

type DietRecordFoodItem struct {
	FoodId   int64   `json:"foodId" v:"required" dc:"食物ID"`
	Name     string  `json:"name" v:"required" dc:"食物名称"`
	Amount   float64 `json:"amount" v:"required|min:0" dc:"食用量"`
	Unit     string  `json:"unit" v:"required" dc:"单位"`
	Calories float64 `json:"calories" v:"min:0" dc:"卡路里"`
	Protein  float64 `json:"protein" v:"min:0" dc:"蛋白质(g)"`
	Fat      float64 `json:"fat" v:"min:0" dc:"脂肪(g)"`
	Carbs    float64 `json:"carbs" v:"min:0" dc:"碳水(g)"`
	Grams    float64 `json:"grams" v:"min:0" dc:"克数"`
}

// 饮食记录列表查询请求
type DietRecordListReq struct {
	g.Meta    `path:"/" tags:"Diet" method:"get" summary:"获取饮食记录列表"`
	Page      int    `json:"page" v:"min:1" dc:"页码" default:"1"`
	Size      int    `json:"size" v:"between:1,100" dc:"每页数量" default:"10"`
	StartDate string `json:"startDate" v:"date" dc:"开始日期"`
	EndDate   string `json:"endDate" v:"date" dc:"结束日期"`
	MealType  string `json:"mealType" v:"in:breakfast,lunch,dinner,snacks" dc:"餐次类型"`
	UserId    *int64 `json:"userId" dc:"用户ID（管理员查询用）"`
}

type DietRecordListRes struct {
	List  []DietRecordItem `json:"list" dc:"饮食记录列表"`
	Total int64            `json:"total" dc:"总数"`
	Page  int              `json:"page" dc:"当前页"`
	Size  int              `json:"size" dc:"每页数量"`
}

type DietRecordItem struct {
	Id           int64                `json:"id" dc:"记录ID"`
	UserId       int64                `json:"userId" dc:"用户ID"`
	Username     string               `json:"username" dc:"用户名"`
	Date         string               `json:"date" dc:"记录日期"`
	Time         string               `json:"time" dc:"记录时间"`
	MealType     string               `json:"mealType" dc:"餐次类型"`
	Remark       string               `json:"remark" dc:"备注信息"`
	TotalCalorie float64              `json:"totalCalorie" dc:"总热量(千卡)"`
	Foods        []DietRecordFoodItem `json:"foods" dc:"食物列表"`
}

// 饮食记录详情查询请求
type DietRecordDetailReq struct {
	g.Meta `path:"/{id}" tags:"Diet" method:"get" summary:"获取饮食记录详情"`
	Id     int64 `json:"id" v:"required" dc:"记录ID"`
}

type DietRecordDetailRes struct {
	Id           int64                `json:"id" dc:"记录ID"`
	UserId       int64                `json:"userId" dc:"用户ID"`
	Username     string               `json:"username" dc:"用户名"`
	Date         string               `json:"date" dc:"记录日期"`
	Time         string               `json:"time" dc:"记录时间"`
	MealType     string               `json:"mealType" dc:"餐次类型"`
	Remark       string               `json:"remark" dc:"备注信息"`
	TotalCalorie float64              `json:"totalCalorie" dc:"总热量(千卡)"`
	Foods        []DietRecordFoodItem `json:"foods" dc:"食物列表"`
}

// 饮食记录更新请求
type DietRecordUpdateReq struct {
	g.Meta       `path:"/{id}" tags:"Diet" method:"put" summary:"更新饮食记录"`
	Id           int64                `json:"id" v:"required" dc:"记录ID"`
	Date         string               `json:"date" v:"date" dc:"记录日期"`
	Time         string               `json:"time" dc:"记录时间"`
	MealType     string               `json:"mealType" v:"in:breakfast,lunch,dinner,snacks" dc:"餐次类型"`
	Remark       string               `json:"remark" dc:"备注信息"`
	TotalCalorie float64              `json:"totalCalorie" v:"min:0" dc:"总热量(千卡)"`
	Foods        []DietRecordFoodItem `json:"foods" dc:"食物列表"`
}

type DietRecordUpdateRes struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}

// 饮食记录删除请求
type DietRecordDeleteReq struct {
	g.Meta `path:"/{id}" tags:"Diet" method:"delete" summary:"删除饮食记录"`
	Id     int64 `json:"id" v:"required" dc:"记录ID"`
}

type DietRecordDeleteRes struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}
