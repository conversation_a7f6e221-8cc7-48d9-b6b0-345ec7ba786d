package diet

import (
	"context"

	"shikeyinxiang-goframe/api/diet/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) DietRecordDelete(ctx context.Context, req *v1.DietRecordDeleteReq) (res *v1.DietRecordDeleteRes, err error) {
	// 1. 调用业务逻辑 - DeleteDietRecord方法只需要ID参数，直接传递
	err = service.Diet().DeleteDietRecord(ctx, req.Id)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 2. 构造成功响应
	res = &v1.DietRecordDeleteRes{
		Success: true,
		Message: "删除成功",
	}

	return res, nil
}
