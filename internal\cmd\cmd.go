package cmd

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gcmd"

	"shikeyinxiang-goframe/internal/controller/auth"
	"shikeyinxiang-goframe/internal/controller/dashboard"
	"shikeyinxiang-goframe/internal/controller/diet"
	"shikeyinxiang-goframe/internal/controller/food"
	"shikeyinxiang-goframe/internal/controller/nutrition"
	"shikeyinxiang-goframe/internal/controller/user"
	"shikeyinxiang-goframe/internal/middleware"
)

var (
	Main = gcmd.Command{
		Name:  "main",
		Usage: "main",
		Brief: "start http server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			s := g.Server()

			// 根路由组 - 应用全局中间件（CORS + Response）
			s.Group("/", func(group *ghttp.RouterGroup) {
				// 注册全局中间件
				middleware.Middleware.RegisterMiddlewares(group)

				// 1. 公开路由组 - 认证相关接口，不需要JWT认证
				group.Group("/api/auth", func(authGroup *ghttp.RouterGroup) {
					authGroup.Bind(auth.NewV1())
				})

				// 2. 用户认证路由组 - 需要JWT认证但不需要特定角色
				group.Group("/", func(userGroup *ghttp.RouterGroup) {
					// 添加JWT认证中间件
					userGroup.Middleware(middleware.Auth)

					// 用户个人信息相关 - /user/**
					userGroup.Group("/user", func(userInfoGroup *ghttp.RouterGroup) {
						userInfoGroup.Bind(user.NewV1())
					})

					// 食物查询 - /api/food/**
					userGroup.Group("/api/food", func(foodGroup *ghttp.RouterGroup) {
						foodGroup.Bind(food.NewV1())
					})

					// 饮食记录 - /api/diet-records/**
					userGroup.Group("/api/diet-records", func(dietGroup *ghttp.RouterGroup) {
						dietGroup.Bind(diet.NewV1())
					})

					// 营养分析 - /api/nutrition/** 和 /api/health/**
					userGroup.Group("/api/nutrition", func(nutritionGroup *ghttp.RouterGroup) {
						nutritionGroup.Bind(nutrition.NewV1())
					})
					userGroup.Group("/api/health", func(healthGroup *ghttp.RouterGroup) {
						healthGroup.Bind(nutrition.NewV1())
					})
				})

				// 3. 管理员路由组 - 需要JWT认证和管理员权限
				group.Group("/api/admin", func(adminGroup *ghttp.RouterGroup) {
					// 添加JWT认证中间件和管理员权限中间件
					adminGroup.Middleware(middleware.Auth)
					adminGroup.Middleware(middleware.AdminAuth)

					// 管理员用户管理 - /api/admin/users/**
					adminGroup.Group("/users", func(adminUserGroup *ghttp.RouterGroup) {
						adminUserGroup.Bind(user.NewV1())
					})

					// 管理员食物管理 - /api/admin/food/**
					adminGroup.Group("/food", func(adminFoodGroup *ghttp.RouterGroup) {
						adminFoodGroup.Bind(food.NewV1())
					})

					// 管理员饮食记录管理 - /api/admin/diet-records/**
					adminGroup.Group("/diet-records", func(adminDietGroup *ghttp.RouterGroup) {
						adminDietGroup.Bind(diet.NewV1())
					})

					// 管理员营养分析管理 - /api/admin/nutrition/**
					adminGroup.Group("/nutrition", func(adminNutritionGroup *ghttp.RouterGroup) {
						adminNutritionGroup.Bind(nutrition.NewV1())
					})

					// 管理员仪表盘 - /api/admin/dashboard/**
					adminGroup.Group("/dashboard", func(dashboardGroup *ghttp.RouterGroup) {
						dashboardGroup.Bind(dashboard.NewV1())
					})
				})
			})

			s.Run()
			return nil
		},
	}
)
