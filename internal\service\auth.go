package service

import (
	"context"
	"shikeyinxiang-goframe/internal/model"
)

type IAuth interface {
	// UserLogin 用户登录
	UserLogin(ctx context.Context, in *model.UserLoginInput) (*model.UserLoginOutput, error)

	// AdminLogin 管理员登录
	AdminLogin(ctx context.Context, in *model.AdminLoginInput) (*model.AdminLoginOutput, error)

	// WechatLogin 微信登录
	WechatLogin(ctx context.Context, in *model.WechatLoginInput) (*model.WechatLoginOutput, error)

	// Register 用户注册
	Register(ctx context.Context, in *model.RegisterInput) (*model.RegisterOutput, error)

	// Logout 登出
	Logout(ctx context.Context, in *model.LogoutInput) (*model.LogoutOutput, error)

	// CurrentUser 获取当前用户信息
	CurrentUser(ctx context.Context, in *model.CurrentUserInput) (*model.CurrentUserOutput, error)

	// ChangePassword 修改密码
	ChangePassword(ctx context.Context, in *model.ChangePasswordInput) (*model.ChangePasswordOutput, error)
}

var localAuth IAuth

func Auth() IAuth {
	if localAuth == nil {
		panic("implement not found for interface IAuth, forgot register?")
	}
	return localAuth
}

func RegisterAuth(i IAuth) {
	localAuth = i
}
