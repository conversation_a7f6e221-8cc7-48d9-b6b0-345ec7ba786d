package nutrition

import (
	"context"

	"shikeyinxiang-goframe/api/nutrition/v1"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) AdminAdviceDelete(ctx context.Context, req *v1.AdminAdviceDeleteReq) (res *v1.AdminAdviceDeleteRes, err error) {
	// 1. 调用业务逻辑
	err = service.NutritionAdvice().DeleteAdvice(ctx, req.Id)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 2. 参数转换：业务层Output → API响应
	res = &v1.AdminAdviceDeleteRes{
		Success: true,
		Message: "删除成功",
	}

	return res, nil
}
