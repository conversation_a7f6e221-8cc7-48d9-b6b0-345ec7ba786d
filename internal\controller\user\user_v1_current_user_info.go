package user

import (
	"context"

	"shikeyinxiang-goframe/api/user/v1"
	"shikeyinxiang-goframe/internal/consts"
	"shikeyinxiang-goframe/internal/middleware"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) CurrentUserInfo(ctx context.Context, req *v1.CurrentUserInfoReq) (res *v1.CurrentUserInfoRes, err error) {
	// 1. 从上下文获取当前用户信息
	user := ctx.Value(consts.CtxUserInfo).(*middleware.UserInfo)

	// 2. 参数转换：API请求参数 → 业务层Input
	input := &model.UserDetailQueryInput{
		UserId: user.UserId,
	}

	// 3. 调用业务逻辑
	output, err := service.User().GetUserDetail(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 4. 参数转换：业务层Output → API响应
	res = &v1.CurrentUserInfoRes{
		Id:         output.UserInfo.Id,
		Username:   output.UserInfo.Username,
		Email:      output.UserInfo.Email,
		Role:       output.UserInfo.Role,
		Status:     output.UserInfo.Status,
		CreateTime: output.UserInfo.CreateTime.String(),
		AvatarUrl:  output.UserInfo.AvatarUrl,
		Phone:      output.UserInfo.Phone,
	}

	return res, nil
}
