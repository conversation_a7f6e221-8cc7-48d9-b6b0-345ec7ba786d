package auth

import (
	"context"

	"shikeyinxiang-goframe/api/auth/v1"
	"shikeyinxiang-goframe/internal/consts"
	"shikeyinxiang-goframe/internal/middleware"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) CurrentUser(ctx context.Context, req *v1.CurrentUserReq) (res *v1.CurrentUserRes, err error) {
	// 1. 从上下文获取当前用户信息
	user := ctx.Value(consts.CtxUserInfo).(*middleware.UserInfo)

	// 2. 参数转换：API请求参数 → 业务层Input
	input := &model.CurrentUserInput{
		UserId: user.UserId,
	}

	// 3. 调用业务逻辑
	output, err := service.Auth().CurrentUser(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 4. 参数转换：业务层Output → API响应
	res = &v1.CurrentUserRes{
		Id:         output.UserInfo.Id,
		Username:   output.UserInfo.Username,
		Email:      output.UserInfo.Email,
		Role:       output.UserInfo.Role,
		Status:     output.UserInfo.Status,
		CreateTime: output.UserInfo.CreateTime.String(),
		AvatarUrl:  output.UserInfo.AvatarUrl,
	}

	return res, nil
}
