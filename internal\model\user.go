package model

import "github.com/gogf/gf/v2/os/gtime"

// UserCreateInput 用户创建输入参数
type UserCreateInput struct {
	Username  string `json:"username" v:"required|length:3,20" dc:"用户名"`
	Email     string `json:"email" v:"required|email" dc:"邮箱"`
	Password  string `json:"password" dc:"密码"`
	Phone     string `json:"phone" dc:"手机号"`
	Role      string `json:"role" dc:"角色"`
	Status    int    `json:"status" dc:"状态"`
	AvatarUrl string `json:"avatarUrl" dc:"头像URL"`
	Openid    string `json:"openid" dc:"微信OpenID"`
}

// UserCreateOutput 用户创建输出结果
type UserCreateOutput struct {
	UserInfo *UserInfo `json:"userInfo" dc:"用户信息"`
}

// UserUpdateInput 用户更新输入参数
type UserUpdateInput struct {
	Id        int64  `json:"id" v:"required" dc:"用户ID"`
	Username  string `json:"username" dc:"用户名"`
	Email     string `json:"email" dc:"邮箱"`
	Role      string `json:"role" dc:"角色"`
	Status    int    `json:"status" dc:"状态"`
	AvatarUrl string `json:"avatarUrl" dc:"头像URL"`
	Password  string `json:"password" dc:"密码"`
}

// UserUpdateOutput 用户更新输出结果
type UserUpdateOutput struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}

// UserStatusUpdateInput 用户状态更新输入参数
type UserStatusUpdateInput struct {
	UserId int64 `json:"userId" v:"required" dc:"用户ID"`
	Status int   `json:"status" v:"required|in:0,1" dc:"状态：0-禁用，1-启用"`
}

// UserStatusUpdateOutput 用户状态更新输出结果
type UserStatusUpdateOutput struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}

// UserQueryInput 用户查询输入参数
type UserQueryInput struct {
	Page    int    `json:"page" v:"required|min:1" dc:"页码"`
	Size    int    `json:"size" v:"required|min:1|max:100" dc:"每页数量"`
	Status  *int   `json:"status" dc:"状态筛选"`
	Keyword string `json:"keyword" dc:"关键词搜索"`
	Role    string `json:"role" dc:"角色筛选"`
}

// UserListOutput 用户列表输出结果
type UserListOutput struct {
	List  []*UserInfo `json:"list" dc:"用户列表"`
	Total int64       `json:"total" dc:"总数"`
	Page  int         `json:"page" dc:"当前页"`
	Size  int         `json:"size" dc:"每页数量"`
}

// PasswordVerifyInput 密码验证输入参数
type PasswordVerifyInput struct {
	UsernameOrEmail string `json:"usernameOrEmail" v:"required" dc:"用户名或邮箱"`
	Password        string `json:"password" v:"required" dc:"密码"`
}

// PasswordVerifyOutput 密码验证输出结果
type PasswordVerifyOutput struct {
	Valid   bool   `json:"valid" dc:"是否验证通过"`
	Message string `json:"message" dc:"验证结果消息"`
}

// UserAvatarUpdateInput 用户头像更新输入参数
type UserAvatarUpdateInput struct {
	UserId    int64  `json:"userId" v:"required" dc:"用户ID"`
	AvatarUrl string `json:"avatarUrl" v:"required" dc:"头像URL"`
}

// UserAvatarUpdateOutput 用户头像更新输出结果
type UserAvatarUpdateOutput struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}

// AvatarUploadUrlInput 头像上传URL生成输入参数
type AvatarUploadUrlInput struct {
	UserId      int64  `json:"userId" v:"required" dc:"用户ID"`
	ContentType string `json:"contentType" v:"required" dc:"文件类型"`
}

// AvatarUploadUrlOutput 头像上传URL生成输出结果
type AvatarUploadUrlOutput struct {
	UploadUrl string `json:"uploadUrl" dc:"上传URL"`
	FileName  string `json:"fileName" dc:"文件名"`
	AvatarUrl string `json:"avatarUrl" dc:"头像访问URL"`
}

// AvatarDownloadUrlInput 头像下载URL生成输入参数
type AvatarDownloadUrlInput struct {
	UserId int64 `json:"userId" v:"required" dc:"用户ID"`
}

// AvatarDownloadUrlOutput 头像下载URL生成输出结果
type AvatarDownloadUrlOutput struct {
	AvatarUrl string `json:"avatarUrl" dc:"头像访问URL"`
	FileName  string `json:"fileName" dc:"文件名"`
}

// ==================== 批量查询用户 ====================

// UserBatchQueryInput 批量查询用户输入
type UserBatchQueryInput struct {
	UserIds []int64 `json:"userIds" dc:"用户ID列表"`
}

// UserBatchQueryOutput 批量查询用户输出
type UserBatchQueryOutput struct {
	Users []UserItem `json:"users" dc:"用户列表"`
}

// UserItem 用户项
type UserItem struct {
	Id       int64  `json:"id" dc:"用户ID"`
	Username string `json:"username" dc:"用户名"`
	Email    string `json:"email" dc:"邮箱"`
	Role     string `json:"role" dc:"角色"`
	Status   int    `json:"status" dc:"状态"`
}

// ==================== 用户营养目标 ====================

// UserNutritionGoal 用户营养目标
type UserNutritionGoal struct {
	UserId          int64       `json:"userId" dc:"用户ID"`
	CalorieGoal     int         `json:"calorieGoal" dc:"热量目标(千卡)"`
	ProteinGoal     float64     `json:"proteinGoal" dc:"蛋白质目标(g)"`
	CarbsGoal       float64     `json:"carbsGoal" dc:"碳水目标(g)"`
	FatGoal         float64     `json:"fatGoal" dc:"脂肪目标(g)"`
	FiberGoal       float64     `json:"fiberGoal" dc:"膳食纤维目标(g)"`
	SugarGoal       float64     `json:"sugarGoal" dc:"糖分目标(g)"`
	SodiumGoal      float64     `json:"sodiumGoal" dc:"钠目标(mg)"`
	PotassiumGoal   float64     `json:"potassiumGoal" dc:"钾目标(mg)"`
	CholesterolGoal float64     `json:"cholesterolGoal" dc:"胆固醇目标(mg)"`
	VitaminAGoal    float64     `json:"vitaminAGoal" dc:"维生素A目标(μg)"`
	VitaminCGoal    float64     `json:"vitaminCGoal" dc:"维生素C目标(mg)"`
	CalciumGoal     float64     `json:"calciumGoal" dc:"钙目标(mg)"`
	IronGoal        float64     `json:"ironGoal" dc:"铁目标(mg)"`
	CreateTime      *gtime.Time `json:"createTime" dc:"创建时间"`
	UpdateTime      *gtime.Time `json:"updateTime" dc:"更新时间"`
}

// UserNutritionGoalQueryInput 查询用户营养目标输入
type UserNutritionGoalQueryInput struct {
	UserId int64 `json:"userId" dc:"用户ID"`
}

// UserNutritionGoalQueryOutput 查询用户营养目标输出
type UserNutritionGoalQueryOutput struct {
	Goal UserNutritionGoal `json:"goal" dc:"营养目标"`
}

// UserNutritionGoalUpdateInput 用户营养目标更新输入参数
type UserNutritionGoalUpdateInput struct {
	UserId           int64   `json:"userId" v:"required" dc:"用户ID"`
	DailyCalorie     float64 `json:"dailyCalorie" v:"min:0" dc:"每日热量目标(千卡)"`
	DailyProtein     float64 `json:"dailyProtein" v:"min:0" dc:"每日蛋白质目标(克)"`
	DailyCarbs       float64 `json:"dailyCarbs" v:"min:0" dc:"每日碳水化合物目标(克)"`
	DailyFat         float64 `json:"dailyFat" v:"min:0" dc:"每日脂肪目标(克)"`
	DailyFiber       float64 `json:"dailyFiber" v:"min:0" dc:"每日膳食纤维目标(克)"`
	DailySugar       float64 `json:"dailySugar" v:"min:0" dc:"每日糖分目标(克)"`
	DailySodium      float64 `json:"dailySodium" v:"min:0" dc:"每日钠目标(毫克)"`
	DailyPotassium   float64 `json:"dailyPotassium" v:"min:0" dc:"每日钾目标(毫克)"`
	DailyCholesterol float64 `json:"dailyCholesterol" v:"min:0" dc:"每日胆固醇目标(毫克)"`
	DailyVitaminA    float64 `json:"dailyVitaminA" v:"min:0" dc:"每日维生素A目标(微克)"`
	DailyVitaminC    float64 `json:"dailyVitaminC" v:"min:0" dc:"每日维生素C目标(毫克)"`
	DailyCalcium     float64 `json:"dailyCalcium" v:"min:0" dc:"每日钙目标(毫克)"`
	DailyIron        float64 `json:"dailyIron" v:"min:0" dc:"每日铁目标(毫克)"`
}

// UserNutritionGoalUpdateOutput 用户营养目标更新输出结果
type UserNutritionGoalUpdateOutput struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}
